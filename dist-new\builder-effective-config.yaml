directories:
  output: dist-new
  buildResources: build
appId: com.tasfiyapro.reconciliation
productName: Tasfiya Pro
files:
  - filter:
      - src/**/*
      - assets/**/*
      - node_modules/**/*
      - package.json
      - '!src/**/*-test.js'
      - '!src/**/*-diagnostic.js'
      - '!src/**/*-verification.js'
      - '!src/**/test-*.js'
      - '!src/test-functions.js'
      - '!src/dialog-test.js'
      - '!src/customer-receipts-test.js'
      - '!src/suppliers-test.js'
      - '!src/new-features-test.js'
      - '!src/bug-fixes-test.js'
      - '!src/advanced-bug-fixes-test.js'
      - '!src/edit-reconciliation-diagnostic.js'
      - '!src/direct-edit-test.js'
      - '!src/reconciliation-debug-test.js'
      - '!src/advanced-print-system-test.js'
      - '!src/bank-receipts-fix-test.js'
      - '!src/edit-data-loading-diagnostic.js'
      - '!src/quick-edit-test.js'
      - '!src/final-edit-verification.js'
      - '!src/save-and-calculation-test.js'
      - '!src/export-print-test.js'
      - '!src/pdf-export-test.js'
      - '!src/filter-save-button-fix-test.js'
      - '!src/performance-pdf-test.js'
      - '!src/transfer-operation-test.js'
      - '!.env'
      - '!.env.local'
      - '!.env.development'
      - '!**/*.log'
      - '!**/*.tmp'
      - '!**/debug.log'
win:
  target:
    - target: nsis
      arch:
        - x64
    - target: portable
      arch:
        - x64
  icon: assets/icon.ico
  requestedExecutionLevel: asInvoker
  artifactName: ${productName}-${version}-${arch}.${ext}
  verifyUpdateCodeSignature: false
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  allowElevation: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: Tasfiya Pro
  deleteAppDataOnUninstall: false
portable:
  artifactName: ${productName}-${version}-portable.${ext}
compression: maximum
copyright: © 2025 محمد أمين الكامل - جميع الحقوق محفوظة
buildDependenciesFromSource: false
nodeGypRebuild: false
publish: null
electronVersion: 37.2.3
